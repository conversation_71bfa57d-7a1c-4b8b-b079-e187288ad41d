<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":media_store_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\media_store_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":workmanager" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\workmanager\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\package_info_plus\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":integration_test" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\integration_test\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\permission_handler_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\shared_preferences_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":webview_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\webview_flutter_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_native_splash" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\flutter_native_splash\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\path_provider_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":sqflite" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\sqflite\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\build\file_picker\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\android\app\src\main\assets"/><source path="D:\000.Workspace\Lekky\build\app\intermediates\shader_assets\debug\out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\000.Workspace\Lekky\android\app\src\debug\assets"/></dataSet></merger>
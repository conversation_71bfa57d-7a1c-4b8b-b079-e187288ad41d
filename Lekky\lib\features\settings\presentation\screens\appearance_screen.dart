import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/theme_provider.dart' as theme_provider;
import '../../../../core/shared/models/theme_mode.dart';

/// Appearance settings screen
class AppearanceScreen extends ConsumerWidget {
  /// Constructor
  const AppearanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeAsync = ref.watch(theme_provider.themeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Options'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: themeAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading theme: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(theme_provider.themeProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (themeState) => ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Theme mode card
            Card(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.palette, color: Colors.blue),
                        SizedBox(width: 16),
                        Text(
                          'Theme Mode',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // System theme option
                    RadioListTile<AppThemeMode>(
                      title: const Text('System Default'),
                      subtitle: const Text('Follow system theme settings'),
                      value: AppThemeMode.system,
                      groupValue: themeState.themeMode,
                      onChanged: (value) => _updateTheme(ref, value, context),
                      secondary: const Icon(Icons.brightness_auto),
                    ),

                    // Light theme option
                    RadioListTile<AppThemeMode>(
                      title: const Text('Light Mode'),
                      subtitle: const Text('Always use light theme'),
                      value: AppThemeMode.light,
                      groupValue: themeState.themeMode,
                      onChanged: (value) => _updateTheme(ref, value, context),
                      secondary: const Icon(Icons.light_mode),
                    ),

                    // Dark theme option
                    RadioListTile<AppThemeMode>(
                      title: const Text('Dark Mode'),
                      subtitle: const Text('Always use dark theme'),
                      value: AppThemeMode.dark,
                      groupValue: themeState.themeMode,
                      onChanged: (value) => _updateTheme(ref, value, context),
                      secondary: const Icon(Icons.dark_mode),
                    ),
                  ],
                ),
              ),
            ),

            // Theme preview card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.preview, color: Colors.blue),
                        SizedBox(width: 16),
                        Text(
                          'Theme Preview',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Preview container
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline,
                          width: 1.0,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Sample Text',
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'This is how text will appear in the app.',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              ElevatedButton(
                                onPressed: () {},
                                child: const Text('Primary Button'),
                              ),
                              OutlinedButton(
                                onPressed: () {},
                                child: const Text('Secondary Button'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Note about theme changes
                    const Text(
                      'Note: Theme changes apply immediately across the app.',
                      style: TextStyle(
                        fontSize: 14,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Update theme with haptic feedback and error handling
  void _updateTheme(
      WidgetRef ref, AppThemeMode? mode, BuildContext context) async {
    if (mode == null) return;

    try {
      // Haptic feedback for theme change
      HapticFeedback.selectionClick();

      // Update theme through provider
      await ref
          .read(theme_provider.themeProvider.notifier)
          .updateThemeMode(mode);

      // Show success feedback
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Theme changed to ${mode.displayName}'),
            duration: const Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (error) {
      // Show error feedback
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change theme: $error'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _updateTheme(ref, mode, context),
            ),
          ),
        );
      }
    }
  }
}

import '../../../../core/models/average.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/models/per_reading_average.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/performance_monitor.dart';
import '../../../../core/utils/average_calculator.dart';
import '../../../../core/utils/average_calculation_lock.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../repositories/average_repository.dart';
import '../repositories/per_reading_average_repository.dart';

/// Service for managing average calculations and caching
class AverageService {
  final AverageRepository _averageRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  /// Constructor
  AverageService({
    AverageRepository? averageRepository,
    PerReadingAverageRepository? perReadingAverageRepository,
    MeterReadingRepository? meterReadingRepository,
    TopUpRepository? topUpRepository,
  })  : _averageRepository =
            averageRepository ?? serviceLocator<AverageRepository>(),
        _perReadingAverageRepository = perReadingAverageRepository ??
            serviceLocator<PerReadingAverageRepository>(),
        _meterReadingRepository =
            meterReadingRepository ?? serviceLocator<MeterReadingRepository>(),
        _topUpRepository = topUpRepository ?? serviceLocator<TopUpRepository>();

  /// Get averages with caching and fallback
  Future<AverageResult> getAverages({bool forceRefresh = false}) async {
    try {
      // If force refresh is requested, skip cache
      if (!forceRefresh) {
        // Try to get cached averages first
        final cachedAverages = await _averageRepository.getLatestAverages();

        if (cachedAverages != null && _isAverageValid(cachedAverages)) {
          Logger.info('AverageService: Using cached averages');
          return AverageResult(
            recentAverage: cachedAverages.recentAverage ?? 0.0,
            totalAverage: cachedAverages.totalAverage ?? 0.0,
            fromCache: true,
          );
        }
      } else {
        Logger.info('AverageService: Force refresh requested, skipping cache');
      }

      // Calculate fresh averages
      Logger.info('AverageService: Calculating fresh averages');
      return await _calculateAndCacheAverages();
    } catch (e) {
      Logger.error('AverageService: Error getting averages: $e');

      // Fallback to direct calculation
      return await _calculateAveragesFallback();
    }
  }

  /// Update averages after data changes
  Future<void> updateAverages() async {
    final lock = AverageCalculationLock();

    await lock.executeWithLock(() async {
      await PerformanceMonitor.timeOperation('average_calculation', () async {
        try {
          Logger.info('AverageService: Updating averages after data change');

          // Fire event to show loading state
          Logger.info('AverageService: Firing averagesCalculating event');
          EventBus().fire(EventType.averagesCalculating);

          await _calculateAndCacheAverages();
          await _calculateAndStorePerReadingAverages();

          Logger.info('AverageService: Average update completed successfully');

          // Fire completion event after averages are fully calculated and cached
          Logger.info('AverageService: Firing dataUpdated event');
          EventBus().fire(EventType.dataUpdated);
          Logger.info('AverageService: dataUpdated event fired successfully');
        } catch (e) {
          Logger.error('AverageService: Error updating averages: $e');

          // Fire event to indicate calculation failed
          EventBus().fire(EventType.averageCalculationFailed);

          rethrow;
        }
      });
    });
  }

  /// Calculate and cache fresh averages
  Future<AverageResult> _calculateAndCacheAverages() async {
    try {
      // Get all data
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      // Convert to MeterEntry format for calculation
      final List<MeterEntry> entries = [
        ...meterReadings.map((reading) => MeterEntry(
              id: reading.id,
              date: reading.date,
              reading: reading.value,
              amountToppedUp: 0.0,
              typeCode: 0,
              notes: reading.notes,
            )),
        ...topUps.map((topUp) => MeterEntry(
              id: topUp.id,
              date: topUp.date,
              reading: 0.0,
              amountToppedUp: topUp.amount,
              typeCode: 1,
              notes: topUp.notes,
            )),
      ];

      // Calculate averages using existing logic
      Logger.info(
          'AverageService: Calculating total average from ${entries.length} entries');
      final totalAverage = AverageCalculator.calculateTotalAverage(entries);
      Logger.info('AverageService: Total average calculated: $totalAverage');

      // Calculate and store per-reading averages for historical accuracy first
      await _calculateAndStorePerReadingAverages();

      // Get recent average from latest per-reading average
      final recentAverage = await _getRecentAverage();

      // Cache the results
      final average = Average(
        recentAverage: recentAverage,
        totalAverage: totalAverage,
        lastUpdated: DateTime.now(),
      );

      await _averageRepository.saveAverages(average);

      Logger.info(
          'AverageService: Calculated and cached averages - Recent: $recentAverage, Total: $totalAverage');

      return AverageResult(
        recentAverage: recentAverage,
        totalAverage: totalAverage,
        fromCache: false,
      );
    } catch (e) {
      Logger.error('AverageService: Error calculating averages: $e');
      rethrow;
    }
  }

  /// Fallback calculation without caching
  Future<AverageResult> _calculateAveragesFallback() async {
    try {
      Logger.info('AverageService: Using fallback calculation');

      // Get all data
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      // Convert to MeterEntry format
      final List<MeterEntry> entries = [
        ...meterReadings.map((reading) => MeterEntry(
              id: reading.id,
              date: reading.date,
              reading: reading.value,
              amountToppedUp: 0.0,
              typeCode: 0,
              notes: reading.notes,
            )),
        ...topUps.map((topUp) => MeterEntry(
              id: topUp.id,
              date: topUp.date,
              reading: 0.0,
              amountToppedUp: topUp.amount,
              typeCode: 1,
              notes: topUp.notes,
            )),
      ];

      // Calculate using existing logic
      final totalAverage = AverageCalculator.calculateTotalAverage(entries);

      // Get recent average from latest per-reading average
      final recentAverage = await _getRecentAverage();

      return AverageResult(
        recentAverage: recentAverage,
        totalAverage: totalAverage,
        fromCache: false,
      );
    } catch (e) {
      Logger.error('AverageService: Fallback calculation failed: $e');
      return const AverageResult(
        recentAverage: 0.0,
        totalAverage: 0.0,
        fromCache: false,
      );
    }
  }

  /// Get recent average from latest per-reading average
  Future<double> _getRecentAverage() async {
    try {
      Logger.info(
          'AverageService: Getting recent average from latest per-reading average');

      // Get the latest per-reading average
      final latestPerReadingAverage =
          await _perReadingAverageRepository.getLatestPerReadingAverage();

      if (latestPerReadingAverage != null) {
        Logger.info(
            'AverageService: Found latest per-reading average: ${latestPerReadingAverage.recentAveragePerDay} for reading date: ${latestPerReadingAverage.readingDate}');
        return latestPerReadingAverage.recentAveragePerDay;
      }

      Logger.info(
          'AverageService: No per-reading averages found, returning 0.0');
      return 0.0;
    } catch (e) {
      Logger.error(
          'AverageService: Error getting recent average from per-reading averages: $e');
      return 0.0;
    }
  }

  /// Calculate and store per-reading averages
  Future<void> _calculateAndStorePerReadingAverages() async {
    try {
      Logger.info('AverageService: Calculating per-reading averages');

      // Get all meter readings sorted by date
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      Logger.info(
          'AverageService: Found ${meterReadings.length} meter readings and ${topUps.length} top-ups');

      if (meterReadings.length < 2) {
        Logger.info(
            'AverageService: Not enough meter readings for per-reading averages (${meterReadings.length})');
        return;
      }

      // Sort readings by date
      meterReadings.sort((a, b) => a.date.compareTo(b.date));

      Logger.info(
          'AverageService: Processing ${meterReadings.length - 1} per-reading average calculations');

      // Calculate recent average for each reading after the first
      for (int i = 1; i < meterReadings.length; i++) {
        final currentReading = meterReadings[i];
        final previousReading = meterReadings[i - 1];

        final days =
            currentReading.date.difference(previousReading.date).inDays;

        Logger.info(
            'AverageService: Processing reading ${i + 1}/${meterReadings.length} - ${currentReading.date} (${days} days from previous)');

        if (days > 0) {
          // Calculate top-ups between these two readings
          double topUpsBetween = 0;
          for (var topUp in topUps) {
            if (topUp.date.isAfter(previousReading.date) &&
                topUp.date.isBefore(currentReading.date)) {
              topUpsBetween += topUp.amount;
            }
          }

          // Calculate usage: previous reading - current reading + top-ups
          final usage =
              previousReading.value - currentReading.value + topUpsBetween;

          Logger.info(
              'AverageService: Reading calculation - Previous: ${previousReading.value}, Current: ${currentReading.value}, Top-ups: $topUpsBetween, Usage: $usage, Days: $days');

          if (usage > 0) {
            final recentAveragePerDay = usage / days;

            Logger.info(
                'AverageService: Calculated per-reading average: $recentAveragePerDay per day for reading ${currentReading.id}');

            // Create and save per-reading average
            final perReadingAverage = PerReadingAverage(
              meterReadingId: currentReading.id!,
              readingDate: currentReading.date,
              recentAveragePerDay: recentAveragePerDay,
              calculatedAt: DateTime.now(),
            );

            await _perReadingAverageRepository
                .savePerReadingAverage(perReadingAverage);
          } else {
            Logger.info(
                'AverageService: Skipping reading ${currentReading.id} - invalid usage: $usage');
          }
        } else {
          Logger.info(
              'AverageService: Skipping reading ${currentReading.id} - invalid days: $days');
        }
      }

      Logger.info('AverageService: Per-reading averages calculated and stored');
    } catch (e) {
      Logger.error(
          'AverageService: Error calculating per-reading averages: $e');
    }
  }

  /// Check if cached average is still valid (less than 1 hour old)
  bool _isAverageValid(Average average) {
    final now = DateTime.now();
    final ageInHours = now.difference(average.lastUpdated).inHours;
    return ageInHours < 1;
  }
}

/// Result class for average calculations
class AverageResult {
  final double recentAverage;
  final double totalAverage;
  final bool fromCache;

  const AverageResult({
    required this.recentAverage,
    required this.totalAverage,
    required this.fromCache,
  });
}
